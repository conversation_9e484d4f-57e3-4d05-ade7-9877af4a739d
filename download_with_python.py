#!/usr/bin/env python3
import urllib.request
import urllib.parse

# URLs for erb_slot_200 images
urls = [
    ("erb_slot_200_1.jpg", "https://cdn.cloud.scenario.com/assets-transform/asset_z635AYJ4AfBF53XvrRtSigNr?p=100&Policy=eyJTdGF0ZW1lbnQiOlt7IlJlc291cmNlIjoiaHR0cHM6Ly9jZG4uY2xvdWQuc2NlbmFyaW8uY29tL2Fzc2V0cy10cmFuc2Zvcm0vYXNzZXRfejYzNUFZSjRBZkJGNTNYdnJSdFNpZ05yP3A9MTAwKiIsIkNvbmRpdGlvbiI6eyJEYXRlTGVzc1RoYW4iOnsiQVdTOkVwb2NoVGltZSI6MTc1MTMyNzk5OX19fV19&Key-Pair-Id=K36FIAB9LE2OLR&Signature=MP9XwrNeAjolKK3CvX8b9JNrubScEHAAlt6RzJgcpVXqURv2c3qpX49GaQBR25xv1V0DCIFdHcSydwrTex6DoDI9SxapbyCpNXiVW7K7hRLm7PfK5syCR1u8Lp9e1Ntbuv5DW3x91yhB%7EgpygTFA27Fp2YPHJZ3U2CYW2p-9FHopEGkr7eAkWBWMJ9uopG9j0V2XIu2Vfb9RypjOkqFIeh729dSyUR50oZUonfW7NNj7ZiEXGSMG9qaMTBKcOos4ywzOzuWAw7GLtWYCim8ukz8sDbrGHJm-u3UmI3Gl0zJK3C2IhBY2B2%7ELXtM4SO4PG5dLmdAdOUtA5YT2MV8%7EFw__"),
    ("erb_slot_200_2.jpg", "https://cdn.cloud.scenario.com/assets-transform/asset_Q7JH2CPDK748Sye7xviLmpep?p=100&Policy=eyJTdGF0ZW1lbnQiOlt7IlJlc291cmNlIjoiaHR0cHM6Ly9jZG4uY2xvdWQuc2NlbmFyaW8uY29tL2Fzc2V0cy10cmFuc2Zvcm0vYXNzZXRfUTdKSDJDUERLNzQ4U3llN3h2aUxtcGVwP3A9MTAwKiIsIkNvbmRpdGlvbiI6eyJEYXRlTGVzc1RoYW4iOnsiQVdTOkVwb2NoVGltZSI6MTc1MTMyNzk5OX19fV19&Key-Pair-Id=K36FIAB9LE2OLR&Signature=j1sB7ryouvnkzah8B6L2NYkj47nNkIRRgIfQc%7EuBuKo%7EO2kRmkMTq57BRVWCBIYCE1AMqk%7EWtiZk8sRFnHLgrFKjPLDsigW5Ak%7E6SPctQGz-k6VFkbb%7EFY19dOsdO0viAvCfNkJGaCmGhvXlDr%7E3WnWQLuii7pn1Qa3tw-YQXtODq%7EHj6EXAkjJuFty8rao4kdjwwgJFrGTsEghU-w-Ar8nT%7EREb-LWgxxbYqzWc456sx0%7EpiIaTQ4Z8TWcY-EwKtUc66MkeTF0CKqzt8tq-p7CE4JDa75DUxO7zarmLH7BKO1gW-cWfPFjFeakkhQVeEPPSRrLLZJ3MXj0L14YakA__"),
    ("erb_slot_200_3.jpg", "https://cdn.cloud.scenario.com/assets-transform/asset_HbT857HL4UN8ymUUfQ8mT5me?p=100&Policy=eyJTdGF0ZW1lbnQiOlt7IlJlc291cmNlIjoiaHR0cHM6Ly9jZG4uY2xvdWQuc2NlbmFyaW8uY29tL2Fzc2V0cy10cmFuc2Zvcm0vYXNzZXRfSGJUODU3SEw0VU44eW1VVWZROG1UNW1lP3A9MTAwKiIsIkNvbmRpdGlvbiI6eyJEYXRlTGVzc1RoYW4iOnsiQVdTOkVwb2NoVGltZSI6MTc1MTMyNzk5OX19fV19&Key-Pair-Id=K36FIAB9LE2OLR&Signature=kMqkyThwYAqal3W-gvwrGfCikrDkDrSZCz73lOCjWHfrClnq2dvux6HQfKEw3wk6Ykrkat4Zs9wu9cHLIHt%7EZyYxy1OI9r4ot6-fYdxYNRnXQHwKv3QHWGkM8OcjgwWdob3BsBNRuBRWzcVJ8WvvMZX3vCpFLKU1C4uBbXaEatnwoTbDFJNTI4QxVPhPL7PM-EwCsjznC3TarQuJ-bFCSFleMdO4mmtgw391gOu2SrRoMFwPUsRCYTdSc1tWDFVOuhs9od8r2Hxegbf4kEv1ViHJDhVgx3dpQppONC3arKbZ4xXLdaPt0ilh6y293U2CAoxqwc-Xeiyhg3VxpSfi1w__"),
    ("erb_slot_200_4.jpg", "https://cdn.cloud.scenario.com/assets-transform/asset_rqbPHyHbnTt9g5xzegBZu85T?p=100&Policy=eyJTdGF0ZW1lbnQiOlt7IlJlc291cmNlIjoiaHR0cHM6Ly9jZG4uY2xvdWQuc2NlbmFyaW8uY29tL2Fzc2V0cy10cmFuc2Zvcm0vYXNzZXRfcnFiUEh5SGJuVHQ5ZzV4emVnQlp1ODVUP3A9MTAwKiIsIkNvbmRpdGlvbiI6eyJEYXRlTGVzc1RoYW4iOnsiQVdTOkVwb2NoVGltZSI6MTc1MTMyNzk5OX19fV19&Key-Pair-Id=K36FIAB9LE2OLR&Signature=uTCEiIPgNnwkqNp04bIdC3bFMJDTA-ZyCrUX%7Ed1xLrbeUTijPNllyL1U1tnCkCeqJ1F-p9nJnWsNYCEPwOkO0qkfWxqXzVQ2bAHxUUo-%7EfoqDK-dJJkOr0GWLfH1n%7EcVenwVfsRiNOtriyBuouz5KA-xqGWHgbGdcqcqI2CdY0eZ3uuC8cP7e32JmW73Op%7EwA6PBYt-tURs8qz73DD4uF6Rigbo8kNYVnXOCL2ITkUQCQ8bH9z-t4SaADl%7EMmSn25Kqe4nRWftBDYTKfXUTQqd0i57YA8j1%7E1UVLrss9u7H%7EGbIeuFLl4xjkmB3WKpkiGPQFkF2x-iAHMNGjyn8tSw__"),
    ("erb_slot_200_5.jpg", "https://cdn.cloud.scenario.com/assets-transform/asset_S85j2me4pgiAhZ5p11zYFnCh?p=100&Policy=eyJTdGF0ZW1lbnQiOlt7IlJlc291cmNlIjoiaHR0cHM6Ly9jZG4uY2xvdWQuc2NlbmFyaW8uY29tL2Fzc2V0cy10cmFuc2Zvcm0vYXNzZXRfUzg1ajJtZTRwZ2lBaFo1cDExellGbkNoP3A9MTAwKiIsIkNvbmRpdGlvbiI6eyJEYXRlTGVzc1RoYW4iOnsiQVdTOkVwb2NoVGltZSI6MTc1MTMyNzk5OX19fV19&Key-Pair-Id=K36FIAB9LE2OLR&Signature=GeYqq7qVH2tUqVSffM4bdmLIOglguE9UkjS%7E1itc%7E7LToDWvmqfq6X5wx6didee%7EOWo-CNtrDnnFL7ID1n7iYjfAmzfQJxJBQ2aiYlN6EIU4mSCUSVRqQlhy9rXvf%7EkntoJk9OWxoZIkqLFHLMox6fSKS6pnQjuKCgUiyFUmrgKVoeqDNAV6x%7E9kkRhtbJx%7ESSzgQijoH0f8-QWzlolmoHMUcICMG1k%7EtJm-gXRyrX5UYt2WXo%7EtqdeoIvpRSFhF27bxUP3ZoXoTyBPiPhnjmoyjoReAhkK7qbblzr%7EleLPW3PMbGtn2u3r12hBACHFuQmE5s276kMfJM65tan2nIw__")
]

for filename, url in urls:
    try:
        print(f"Downloading {filename}...")
        urllib.request.urlretrieve(url, filename)
        print(f"✓ {filename} downloaded successfully")
    except Exception as e:
        print(f"✗ Error downloading {filename}: {e}")

print("Download complete!")
