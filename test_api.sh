#!/bin/bash

# Test API credentials
export SCENARIO_API_KEY="api_ttVVbj1gnKRqUbrZytSiVgf4"
export SCENARIO_API_SECRET="YEu8gr227RctGwqTmAwv2nQp"

echo "🔑 API Key: $SCENARIO_API_KEY"
echo "🔐 API Secret: $SCENARIO_API_SECRET"

# Vytvorenie Basic Auth tokenu
AUTH_TOKEN=$(echo -n "$SCENARIO_API_KEY:$SCENARIO_API_SECRET" | base64)
echo "🎫 Auth Token: $AUTH_TOKEN"

# Test API volania
echo ""
echo "🧪 Testujeme API..."
response=$(curl -s -X GET "https://api.cloud.scenario.com/v1/models" \
    -H "Authorization: Basic $AUTH_TOKEN")

echo "📄 Odpoveď: $response"

if echo "$response" | grep -q "models\|data"; then
    echo "✅ API funguje!"
else
    echo "❌ API nefunguje"
fi
