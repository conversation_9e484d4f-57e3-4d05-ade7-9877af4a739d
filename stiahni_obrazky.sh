#!/bin/bash

# Skript na stiahnutie vygenerovaných obrázkov zo Scenario
export SCENARIO_API_KEY="api_ttVVbj1gnKRqUbrZytSiVgf4"
export SCENARIO_API_SECRET="YEu8gr227RctGwqTmAwv2nQp"
AUTH_TOKEN=$(echo -n "$SCENARIO_API_KEY:$SCENARIO_API_SECRET" | base64)

echo "📥 Sťahujem vygenerované obrázky..."
echo ""

# Job IDs z predchádzajúceho generovania
declare -A jobs=(
    ["job_drKsxdvWmab5UfDTZXeNQ5CH"]="bg_cipher_parchment_1080.png"
    ["job_KVFgngEFYqziVhGt7FZg17vf"]="frame_input_600x128.png"
    ["job_yTq1NkNvkgqkbDkrohs78JEu"]="btn_submit_304x128.png"
    ["job_BPJ56XEoaXFRVewSTJ5Dd2fU"]="icon_error_128x128.png"
    ["job_asWuPCjagMBXwoURLXQUyJNr"]="title_cipher_800x200.png"
)

download_image() {
    local job_id="$1"
    local filename="$2"
    
    echo "🔍 Kontrolujem stav: $filename..."
    
    # Získaj informácie o job
    job_info=$(curl -s -X GET "https://api.cloud.scenario.com/v1/jobs/$job_id" \
        -H "Authorization: Basic $AUTH_TOKEN")
    
    # Skontroluj status
    status=$(echo "$job_info" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
    echo "   Status: $status"
    
    if [ "$status" = "success" ]; then
        # Získaj asset ID
        asset_id=$(echo "$job_info" | grep -o '"assetIds":\["[^"]*"' | cut -d'"' -f4)
        echo "   Asset ID: $asset_id"
        
        if [ -n "$asset_id" ]; then
            # Získaj download URL
            asset_info=$(curl -s -X GET "https://api.cloud.scenario.com/v1/assets/$asset_id" \
                -H "Authorization: Basic $AUTH_TOKEN")
            
            download_url=$(echo "$asset_info" | grep -o '"url":"[^"]*"' | cut -d'"' -f4)
            
            if [ -n "$download_url" ]; then
                echo "   📥 Sťahujem $filename..."
                
                # Stiahni obrázok
                if curl -L -o "$filename" "$download_url"; then
                    file_size=$(ls -la "$filename" | awk '{print $5}')
                    echo "   ✅ $filename stiahnutý ($file_size bajtov)"
                else
                    echo "   ❌ Chyba pri sťahovaní $filename"
                fi
            else
                echo "   ❌ Nepodarilo sa získať download URL"
            fi
        else
            echo "   ❌ Nepodarilo sa získať asset ID"
        fi
    else
        echo "   ⏳ Obrázok sa ešte generuje (status: $status)"
    fi
    echo ""
}

# Stiahni všetky obrázky
for job_id in "${!jobs[@]}"; do
    download_image "$job_id" "${jobs[$job_id]}"
done

echo "🎉 Sťahovanie dokončené!"
echo "📁 Stiahnuté súbory:"
ls -la *.png 2>/dev/null || echo "Žiadne PNG súbory neboli nájdené"

# Vyčistenie
unset SCENARIO_API_KEY
unset SCENARIO_API_SECRET
unset AUTH_TOKEN
