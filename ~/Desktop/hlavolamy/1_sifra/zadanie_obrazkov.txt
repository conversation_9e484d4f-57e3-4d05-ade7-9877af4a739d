# Puzzle 1 – <PERSON><PERSON><PERSON> (style:"Prekliate_dedicstvo")
# API: api_BcbAFAXVD1HcedCEKw7hmhLW

## Zoznam obrázkov na vygenerovanie:

### 1) bg_cipher_parchment_1080.png
- Rozmer: 1080×1920
- Prompt: ancient parchment texture, dark gothic stains, seamless, no text, style:"Prekliate_dedicstvo", no background
- Účel: Pozadie pre šifru

### 2) frame_input_600x90.png  
- Rozmer: 600×90
- Prompt: ornate brass frame, horizontal rectangle, fits text input, dark gothic engraving, no background, style:"Prekliate_dedicstvo"
- Účel: Rám pre textové pole

### 3) btn_submit_300x90.png
- Rozmer: 300×90
- Prompt: rectangular button, embossed gothic pattern, dark gold, high relief, no text, no background, style:"Prekliate_dedicstvo"
- Účel: Tlačidlo na odoslanie

### 4) icon_error_64.png
- Rozmer: 64×64
- Prompt: cracked blood-red wax seal, sharp edges, no background, style:"Prekliate_dedicstvo"
- Účel: Ikona chyby

### 5) title_cipher_800.png
- Rozmer: 800×(auto)
- Prompt: calligraphic banner, text:"Dekóduj správu", black ink on torn parchment strip, no background, style:"Prekliate_dedicstvo"
- Účel: Titulok hlavolamu

## Inštrukcie na použitie API:
1. Použiť API kľúč: api_BcbAFAXVD1HcedCEKw7hmhLW
2. Pre každý obrázok použiť presne zadaný prompt
3. Dodržať presné rozmery
4. Všetky obrázky uložiť do tohto priečinka (1_sifra)
5. Použiť presné názvy súborov ako sú uvedené vyššie
